import asyncio
import base64
import io
import json
import logging
import os
import time
from http import HTTPStatus
from pathlib import Path
from string import Template
from typing import List, Optional

import httpx
import pypdf  # NOTE: You need to add 'pypdf' to your requirements.txt
import contextlib
import inspect # Add inspect module
import tempfile
from dotenv import load_dotenv
from fastapi import (FastAPI, File, Form, HTTPException, Request,
                     UploadFile, Body)
from fastapi.responses import JSONResponse
from fastapi.middleware.cors import CORSMiddleware # Add CORS middleware import
from fastapi.exception_handlers import request_validation_exception_handler
from fastapi.exceptions import RequestValidationError
from ratelimit import sleep_and_retry, limits
from supabase import Client, create_client, AsyncClient, create_async_client
from supabase.lib.client_options import ClientOptions
from gotrue.errors import AuthApiError # Corrected import based on search
from openai import AsyncOpenAI, OpenAIError # Add OpenAI import

from fastapi import BackgroundTasks # Import BackgroundTasks
from pydantic import BaseModel  # Import Pydantic BaseModel

class LegalRequestData(BaseModel):
    user_id: str
    api_key: str
    query: str

import google.generativeai as genai  # Add Gemini import
from google.api_core.exceptions import ResourceExhausted  # Import for rate limit handling

# --- Logging Configuration FIRST ---
# Determine log level from environment variable, default to INFO
log_level_name = os.getenv("LOG_LEVEL", "INFO").upper()
log_level = getattr(logging, log_level_name, logging.INFO)

# Configure logging to output to stdout for Vercel
logging.basicConfig(
    level=log_level,
    format='%(asctime)s.%(msecs)03d %(levelname)s %(module)s: %(message)s',
    datefmt='%Y-%m-%d %H:%M:%S',
    handlers=[logging.StreamHandler()] # Vercel captures stdout
)
# Get the root logger AFTER basicConfig
logger = logging.getLogger(__name__)
logger.info("API server starting...")
logger.info(f"Environment: {os.getenv('VERCEL_ENV', 'development')}")
logger.info(f"Log level: {log_level_name}")
# --- End Logging Configuration ---

# Load prompt template with error handling
script_dir = Path(__file__).parent
prompt_template_path = script_dir / "prompt_template.pt"
try:
    with open(prompt_template_path, "r") as f:
        PROMPT_TEMPLATE = Template(f.read())
    logger.info(f"Prompt template loaded successfully from {prompt_template_path}")
except FileNotFoundError:
    logger.error(f"Prompt template file not found at {prompt_template_path}. Cannot proceed.")
    # Raising an error here will stop the app, which is correct behavior if the template is essential
    raise FileNotFoundError(f"Required prompt template file not found: {prompt_template_path}")
except Exception as e:
    logger.error(f"Error loading prompt template file {prompt_template_path}: {e}", exc_info=True)
    raise # Re-raise other unexpected errors

# Load file analysis prompt template
prompt_file_analysis_path = script_dir / "prompt_file_analysis.pt"
try:
    with open(prompt_file_analysis_path, "r") as f:
        PROMPT_FILE_ANALYSIS = Template(f.read())
    logger.info(f"File analysis prompt template loaded successfully from {prompt_file_analysis_path}")
except FileNotFoundError:
    logger.error(f"File analysis prompt template file not found at {prompt_file_analysis_path}. Cannot proceed.")
    raise FileNotFoundError(f"Required file analysis prompt template file not found: {prompt_file_analysis_path}")
except Exception as e:
    logger.error(f"Error loading file analysis prompt template file {prompt_file_analysis_path}: {e}", exc_info=True)
    raise

# Load .env file from the parent directory (project root)
dotenv_path = Path(__file__).parent.parent / '.env'
logger.info(f"Attempting to load .env file from: {dotenv_path}")
if dotenv_path.is_file():
    load_dotenv(dotenv_path=dotenv_path)
    logger.info(".env file found and loaded.")
else:
    logger.warning(".env file not found at the expected location. Relying on environment variables.")

# Helper function to get boolean value from environment variable
def get_bool_env(var_name, default=True):
    value = os.getenv(var_name, str(default))
    return value.lower() in ("1", "true", "yes", "on")

# Specific loggers can be obtained if needed, but basicConfig covers the root
access_logger = logging.getLogger('access') # Can still use specific loggers
debug_logger = logging.getLogger('debug')   # They will inherit the root config

# Supabase setup
SUPABASE_URL: str = os.getenv("SUPABASE_URL")
SUPABASE_KEY: str = os.getenv("SUPABASE_KEY")

# OpenAI Client Setup (using OpenAI library)
openai_api_key: str = os.getenv("LLM_API_KEY")
openai_api_key1: str = os.getenv("LLM_API_KEY1") # New API key
gemini_base_url: str = os.getenv("GEMINI_BASE_URL", "https://generativelanguage.googleapis.com/v1beta/openai/")
gemini_model: str = os.getenv("GEMINI_MODEL", "gemini-2.0-flash")

# Gemini Client Setup
gemini_api_key: str = os.getenv("GEMINI_API_KEY")

# Collect available Gemini keys including fallback
gemini_keys_raw = [gemini_api_key, openai_api_key, openai_api_key1]
gemini_keys = [k for k in gemini_keys_raw if k]
# Environment variable checks
logger.info("Checking required environment variables...")
required_vars = {
    "SUPABASE_URL": SUPABASE_URL,
    "SUPABASE_KEY": SUPABASE_KEY,
    "LLM_API_KEY": openai_api_key,
    "GEMINI_BASE_URL": gemini_base_url,
    "GEMINI_MODEL": gemini_model,
    "GEMINI_API_KEY": gemini_api_key
}
# LLM_API_KEY1 is optional, but at least one LLM key should be present
if not openai_api_key and not openai_api_key1:
    if not gemini_api_key:
        missing_vars = ["GEMINI_API_KEY (no LLM keys available)"]
    else:
        missing_vars = [name for name, value in required_vars.items() if not value]
else:
    missing_vars = [name for name, value in required_vars.items() if not value and name != "GEMINI_API_KEY"]

if missing_vars:
    error_message = f"Missing required environment variables: {', '.join(missing_vars)}"
    logger.error(error_message)
    raise EnvironmentError(error_message)
else:
    logger.info("All required environment variables seem to be present.")

# Initialize FastAPI app instance
logger.info("Defining FastAPI app instance...")
app = FastAPI()
logger.info("FastAPI app instance defined successfully.")



async def initialize_app_state():
    logger.info("Initializing app state (Supabase and OpenAI clients)...")
    # Initialize Supabase client
    try:
        if not SUPABASE_URL or not SUPABASE_KEY:
            raise ValueError("Supabase URL or Key not set in environment variables.")
        
        from gotrue._async.storage import AsyncMemoryStorage
        client_options = ClientOptions(
            headers={"Accept": "application/json"},
            storage=AsyncMemoryStorage()
        )
        app.state.supabase = await create_async_client(SUPABASE_URL, SUPABASE_KEY, options=client_options)
        logger.info(f"Supabase client initialized successfully. Type: {type(app.state.supabase)}")
    except Exception as e:
        logger.error(f"Failed to initialize Supabase client: {e}", exc_info=True)
        raise # Re-raise to prevent app from starting with uninitialized Supabase

    # Initialize OpenAI clients
    logger.info("Initializing OpenAI clients for Gemini...")
    openai_clients = []
    try:
        if openai_api_key:
            client_0 = AsyncOpenAI(api_key=openai_api_key, base_url=gemini_base_url)
            openai_clients.append(client_0)
            logger.info("OpenAI client (key 0) initialized.")
        else:
            logger.warning("LLM_API_KEY not set. Only using LLM_API_KEY1 if available.")

        if openai_api_key1:
            client_1 = AsyncOpenAI(api_key=openai_api_key1, base_url=gemini_base_url)
            openai_clients.append(client_1)
            logger.info("OpenAI client (key 1) initialized.")
        else:
            logger.warning("LLM_API_KEY1 not set. Only using LLM_API_KEY if available.")

        if not openai_clients:
            raise ValueError("No LLM API keys found (LLM_API_KEY or LLM_API_KEY1). At least one is required.")

        # Note: Skipping verification for OpenAI clients when using Gemini API due to timeout issues
        # The clients are still initialized and can be used for requests
        app.state.openai_clients = openai_clients
        app.state.openai_client_index = 0 # Initialize index for round-robin
        app.state.openai_initialized = True
        logger.info(f"All {len(openai_clients)} OpenAI clients for Gemini initialized (verification skipped).")

    except Exception as e:
        logger.error(f"Failed to initialize OpenAI clients: {e}", exc_info=True)
        raise # Re-raise to prevent app from starting with uninitialized OpenAI clients

    # Initialize Gemini clients and verify API keys
    logger.info("Initializing and verifying Gemini API keys...")
    try:
        verified_keys = []
        for key in gemini_keys:
            try:
                genai.configure(api_key=key)
                # Test with a lightweight call
                models = genai.list_models()
                if models:
                    verified_keys.append(key)
                    logger.info(f"Verified Gemini API key ending with '**{key[-4:] if key else ''}' as valid")
                else:
                    logger.warning(f"Gemini API key ending with '**{key[-4:] if key else ''}' returned no models")
            except Exception as e:
                logger.error(f"Failed to verify Gemini API key ending with '**{key[-4:] if key else ''}': {e}")

        if not verified_keys:
            raise ValueError("No valid Gemini API keys found")

        app.state.verified_gemini_keys = verified_keys
        app.state.gemini_key_index = 0
        logger.info(f"Successfully validated {len(verified_keys)} Gemini API keys")
        app.state.gemini_initialized = True

    except Exception as e:
        logger.error(f"Failed to initialize Gemini client: {e}", exc_info=True)
        raise

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # Allow all origins
    allow_credentials=True,
    allow_methods=["*"],  # Allow all methods
    allow_headers=["*"],  # Allow all headers
)

# Custom exception handler to sanitize all error responses
@app.exception_handler(Exception)
async def global_exception_handler(request: Request, exc: Exception):
    """
    Global exception handler to ensure all error responses are properly sanitized
    and don't contain binary data that could cause UnicodeDecodeError.
    """
    logger.error(f"Global exception handler caught: {exc}", exc_info=True)

    if isinstance(exc, HTTPException):
        # Sanitize HTTPException details
        exc.detail = sanitize_error_detail(exc.detail)
        return JSONResponse(
            status_code=exc.status_code,
            content={"detail": exc.detail},
            headers=getattr(exc, 'headers', None)
        )
    else:
        # For other exceptions, return a safe generic error
        return JSONResponse(
            status_code=500,
            content={"detail": "Internal server error occurred during request processing"}
        )

# Custom JSON Response class that safely handles binary data
class SafeJSONResponse(JSONResponse):
    """
    Custom JSONResponse that uses a safe JSON encoder to handle binary data
    and prevent UnicodeDecodeError during serialization.
    """

    def render(self, content: any) -> bytes:
        # Sanitize the content before encoding
        sanitized_content = sanitize_error_detail(content)
        return json.dumps(
            sanitized_content,
            ensure_ascii=False,
            allow_nan=False,
            indent=None,
            separators=(",", ":"),
        ).encode("utf-8")

# Override FastAPI's default exception handlers
@app.exception_handler(RequestValidationError)
async def custom_request_validation_exception_handler(request: Request, exc: RequestValidationError):
    """
    Custom handler for RequestValidationError that sanitizes error details
    to prevent UnicodeDecodeError during JSON serialization.
    """
    logger.error(f"RequestValidationError: {exc}", exc_info=True)

    # Sanitize the error details to remove any binary data
    sanitized_errors = []
    for error in exc.errors():
        sanitized_error = sanitize_error_detail(error)
        sanitized_errors.append(sanitized_error)

    return SafeJSONResponse(
        status_code=422,
        content={"detail": sanitized_errors},
    )

@app.exception_handler(HTTPException)
async def custom_http_exception_handler(request: Request, exc: HTTPException):
    """
    Custom handler for HTTPException that sanitizes error details
    to prevent UnicodeDecodeError during JSON serialization.
    """
    logger.error(f"HTTPException: Status={exc.status_code}, Detail={exc.detail}")

    # Sanitize the error detail
    sanitized_detail = sanitize_error_detail(exc.detail)

    return SafeJSONResponse(
        status_code=exc.status_code,
        content={"detail": sanitized_detail},
        headers=getattr(exc, 'headers', None),
    )

# --- File Handling ---

# Allowed file types
ALLOWED_FILE_TYPES = {
    "application/pdf",
    "text/plain",
    "image/png",
    "image/jpeg",
    "image/webp",
}
IMAGE_FILE_TYPES = {"image/png", "image/jpeg", "image/webp"}

def validate_file_signature(file_content: bytes, expected_mime_type: str) -> bool:
    """
    Validates file content against expected MIME type by checking file signatures (magic numbers).
    Returns True if the file signature matches the expected type, False otherwise.
    """
    if not file_content:
        return False

    # File signature mappings
    signatures = {
        "application/pdf": [b'%PDF-'],
        "text/plain": [],  # Text files don't have reliable magic numbers, validated separately
        "image/png": [b'\x89PNG\r\n\x1a\n'],
        "image/jpeg": [b'\xff\xd8'],
        "image/webp": [b'RIFF'],
    }

    expected_signatures = signatures.get(expected_mime_type, [])
    if not expected_signatures:
        # For types without specific signatures (like text/plain), we'll rely on other validation
        return True

    # Check if file starts with any of the expected signatures
    for signature in expected_signatures:
        if file_content.startswith(signature):
            # Additional validation for WebP (needs to check RIFF header + WEBP)
            if expected_mime_type == "image/webp":
                if len(file_content) >= 12 and file_content[8:12] == b'WEBP':
                    return True
            else:
                return True

    return False

def sanitize_error_detail(detail):
    """
    Sanitizes error detail to remove binary data that could cause UnicodeDecodeError
    during JSON serialization.
    """
    if isinstance(detail, dict):
        # Recursively sanitize dictionary values
        return {k: sanitize_error_detail(v) for k, v in detail.items()}
    elif isinstance(detail, list):
        # Recursively sanitize list items
        return [sanitize_error_detail(item) for item in detail]
    elif isinstance(detail, bytes):
        # Convert bytes to a safe string representation
        try:
            return detail.decode('utf-8')
        except UnicodeDecodeError:
            # If decoding fails, return a safe placeholder
            return f"<binary data ({len(detail)} bytes)>"
    elif isinstance(detail, str):
        # String is already safe
        return detail
    else:
        # For other types, convert to string safely
        try:
            return str(detail)
        except Exception:
            return "<unserializable data>"

async def extract_text_from_pdf(file_content: bytes) -> str:
    """Extracts text from a PDF file's content."""
    try:
        pdf_reader = pypdf.PdfReader(io.BytesIO(file_content))
        text = ""
        for page in pdf_reader.pages:
            page_text = page.extract_text()
            if page_text:
                text += page_text + "\n"
        return text
    except Exception as e:
        logger.error(f"Failed to extract text from PDF: {e}", exc_info=True)
        raise HTTPException(status_code=400, detail="Could not process the PDF file. It may be corrupt or unreadable.")

async def upload_file_to_gemini(file_content: bytes, filename: str, content_type: str) -> str:
    """
    Uploads a file to Gemini Files API and returns the file URI.
    """
    verified_keys = getattr(app.state, "verified_gemini_keys", [])
    # NOTE: genai.configure modifies global state, which is acceptable for typical web server concurrency (per-worker process in gunicorn).
    # For high-concurrency scenarios with multiple threads per process, consider using separate genai clients or locks.
    if not verified_keys:
        logger.error("No verified Gemini API keys available in application state.")
        raise HTTPException(status_code=500, detail="No verified Gemini API keys available.")

    # Determine MIME type for Gemini
    mime_type = content_type
    if content_type == "text/plain":
        mime_type = "text/plain"

    num_keys = len(verified_keys)
    start_index = getattr(app.state, "gemini_key_index", 0)

    for i in range(num_keys):
        key_index = (start_index + i) % num_keys
        current_key = verified_keys[key_index]

        try:
            genai.configure(api_key=current_key)
            # Create a temporary file and write the content to it
            with tempfile.NamedTemporaryFile(delete=False, suffix=Path(filename).suffix) as temp_file:
                temp_file.write(file_content)
                temp_file_path = temp_file.name

            try:
                # Upload file to Gemini
                file_obj = genai.upload_file(path=temp_file_path, display_name=filename, mime_type=mime_type)
                logger.info(f"File uploaded to Gemini: {filename}, URI: {file_obj.uri} using key index {key_index}")
                # On success, update the key index for the next request
                app.state.gemini_key_index = (key_index + 1) % num_keys
                return file_obj.uri
            finally:
                # Ensure the temporary file is deleted
                os.remove(temp_file_path)

        except ResourceExhausted as e:
            logger.warning(f"Gemini key index {key_index} hit rate limit: {e}. Trying next key.")
            if i == num_keys - 1:
                logger.error("All verified Gemini keys hit rate limit.")
                raise HTTPException(
                    status_code=429,
                    detail="All available Gemini API keys have hit rate limits. Please try again later."
                )
            # Continue to next key

        except Exception as e:
            logger.error(f"Failed to upload file to Gemini with key index {key_index}: {e}", exc_info=True)
            # For other exceptions or errors, raise immediately without trying other keys
            raise HTTPException(status_code=500, detail="Failed to upload file to Gemini Files API.")


# Rate limiting setup
MAX_REQUESTS_PER_DAY = int(os.getenv("MAX_REQUESTS_PER_DAY", 1000))
MAX_REQUESTS_PER_MINUTE = int(os.getenv("MAX_REQUESTS_PER_MINUTE", 100))

# Update environment variables with validation
# IMPORTANT: SUPABASE_KEY must be the service role key for read/write access to the profiles table.
if not SUPABASE_KEY:
    logger.error("SUPABASE_KEY (service role key) is not set in environment variables")
    raise ValueError("SUPABASE_KEY (service role key) must be configured")

# Initialize headers with validated key
SUPABASE_HEADERS = {
    "apikey": str(SUPABASE_KEY),  # Use service role key for admin access
    "Authorization": f"Bearer {SUPABASE_KEY}",  # Add Authorization header for service key
    "Content-Type": "application/json"
}

async def check_user_key(request: Request, user_id: str, api_key: str) -> str:
    """
    Verifies the provided user_id and API key using Supabase REST API.
    """
    async with httpx.AsyncClient() as client:
        try:
            # Log the query for debugging
            logger.debug(f"Querying Supabase profiles for user_id={user_id}")
            response = await client.get(
                f"{SUPABASE_URL}/rest/v1/profiles",
                headers=SUPABASE_HEADERS,
                params={
                    "user_id": f"eq.{user_id}",
                    "select": "api_key,credits"
                }
            )
            logger.debug(f"Supabase response status: {response.status_code}, body: {response.text}")

            if response.status_code != 200:
                logger.error(f"Supabase REST API error: {response.status_code} - {response.text}")
                raise HTTPException(
                    status_code=500,
                    detail={
                        "error": "Failed to fetch user profile",
                        "status": response.status_code
                    }
                    )

            profiles = response.json()
            if not profiles:
                logger.error(f"No profile found for user_id={user_id}. Supabase response: {response.text}")
                raise HTTPException(status_code=404, detail=f"User profile not found for user_id={user_id}")

            profile = profiles[0]
            if profile["api_key"] != api_key:
                raise HTTPException(status_code=403, detail="Invalid API key")
            
            if profile["credits"] <= 0:
                raise HTTPException(status_code=403, detail="Insufficient credits")

            return user_id

        except httpx.RequestError as e:
            logger.error(f"HTTP request error: {e}")
            raise HTTPException(
                status_code=500,
                detail={
                    "error": "Failed to connect to authentication service",
                    "detail": str(e)
                }
            )

async def query_openai(request: Request, user_message_content: List[dict]) -> str:
    """
    Queries the configured Generative AI model with a structured message, key rotation, and error handling.
    """
    openai_clients = getattr(request.app.state, "openai_clients", [])
    
    if not openai_clients:
        error_msg = "No OpenAI clients initialized in application state."
        logger.error(error_msg)
        raise HTTPException(
            status_code=500,
            detail={
                "error": error_msg,
                "reason": "Application initialization incomplete",
                "fix": "Verify environment variables (LLM_API_KEY, LLM_API_KEY1) configuration"
            }
        )

    num_clients = len(openai_clients)
    start_index = getattr(request.app.state, "openai_client_index", 0)
    
    # Try all available clients, starting from the current index
    for i in range(num_clients):
        client_index = (start_index + i) % num_clients
        client = openai_clients[client_index]

        try:
            logger.debug(f"Sending message to model {gemini_model} using client (index {client_index})")
            response = await client.chat.completions.create(
                model=gemini_model,
                messages=[
                    {"role": "system", "content": "Tu es un expert en droit français, spécialisé dans les questions juridiques et légales. Tu apportes des réponses claires, précises et fiables, en t'appuyant sur la législation française en vigueur, la jurisprudence pertinente et les principes fondamentaux du droit."},
                    {"role": "user", "content": user_message_content}
                ],
                n=1
            )
            response_text = response.choices[0].message.content
            if not response_text:
                logger.warning(f"Received empty response from AI model using client index {client_index}.")
                # This might be a valid (but empty) response. Let's try the next client just in case it's a client-specific issue.
                continue

            logger.debug(f"Received response from client index {client_index}: {response_text[:200]}...")
            
            # On success, update the index for the *next* request and return
            request.app.state.openai_client_index = (client_index + 1) % num_clients
            return response_text

        except (openai.RateLimitError, openai.APIConnectionError, openai.AuthenticationError) as e:
            logger.warning(f"OpenAI client (index {client_index}) failed with {type(e).__name__}. Trying next client. Error: {e}")
            if i == num_clients - 1: # This was the last client to try
                logger.error(f"All OpenAI clients failed. Last error from client {client_index}: {e}")
                # Raise a specific error based on the last exception type
                if isinstance(e, openai.RateLimitError):
                    raise HTTPException(
                        status_code=HTTPStatus.TOO_MANY_REQUESTS,
                        detail="AI model rate limit exceeded for all available keys. Please try again later."
                    )
                if isinstance(e, openai.AuthenticationError):
                    raise HTTPException(
                        status_code=HTTPStatus.UNAUTHORIZED,
                        detail="AI service authentication failed for all available keys. Check API keys."
                    )
                raise HTTPException(
                    status_code=HTTPStatus.SERVICE_UNAVAILABLE,
                    detail="Could not connect to the AI model service with any available key."
                )
            # Otherwise, the loop will continue to the next client
        except OpenAIError as e:
            logger.error(f"OpenAI API error for client (index {client_index}): {e} (Type: {type(e).__name__})")
            # For other OpenAI errors, it might be a prompt issue, so we don't try other keys. Re-raise.
            raise HTTPException(status_code=500, detail=f"Error querying AI model: {e}")
        except Exception as e:
            logger.error(f"Unexpected error querying AI model with client (index {client_index}): {e}", exc_info=True)
            raise HTTPException(status_code=500, detail=f"Unexpected internal server error: {e}")

    # If the loop finishes without returning, it means all clients failed
    logger.error("All OpenAI clients failed to process the request.")
    raise HTTPException(status_code=500, detail="AI model returned an empty response from all available keys.")

async def consume_credit(request: Request, user_id: str, max_retries: int = 3):
    """
    Decrements the credit count for the given user_id by 1.
    Uses Supabase RPC for atomic operation to avoid race conditions.
    Includes retry logic.
    """
    supabase_client: AsyncClient = app.state.supabase # Use app.state.supabase directly
    if not supabase_client:
        logger.error(f"[consume_credit] Supabase client not found for user_id: {user_id}")
        print(f"[consume_credit] Supabase client not found for user_id: {user_id}", flush=True)
        return

    for attempt in range(1, max_retries + 1):
        try:
            # First verify the user exists and has credits
            try:
                profile_res = await supabase_client.from_("profiles") \
                    .select("credits") \
                    .eq("user_id", user_id) \
                    .single() \
                    .execute()
            except Exception as e:
                logger.error(f"[consume_credit] Supabase profile query error on attempt {attempt} for user {user_id}: {e}")
                print(f"[consume_credit] Supabase profile query error on attempt {attempt} for user {user_id}: {e}", flush=True)
                if attempt < max_retries:
                    await asyncio.sleep(2 ** attempt) # Exponential backoff
                    continue
                else:
                    logger.error(f"[consume_credit] Failed to query profile after {max_retries} attempts for user {user_id}.")
                    return

            if not profile_res.data:
                logger.error(f"[consume_credit] No profile found for user_id: {user_id} on attempt {attempt}. Supabase response: {profile_res.data}")
                print(f"[consume_credit] No profile found for user_id: {user_id} on attempt {attempt}. Supabase response: {profile_res.data}", flush=True)
                return # No point in retrying if profile doesn't exist

            current_credits = profile_res.data.get("credits", 0)
            if current_credits <= 0:
                logger.warning(f"[consume_credit] User {user_id} has no credits ({current_credits}) on attempt {attempt}")
                print(f"[consume_credit] User {user_id} has no credits ({current_credits}) on attempt {attempt}", flush=True)
                return # No point in retrying if no credits

            # Use RPC for atomic decrement
            try:
                rpc_response = await supabase_client.rpc(
                    "decrement_credits",
                    {"user_uuid": user_id}
                ).execute()
                logger.info(f"[consume_credit] Successfully executed decrement RPC for user_id: {user_id} on attempt {attempt}")
                return # Success, exit function
            except Exception as e:
                logger.error(f"[consume_credit] Supabase RPC error on attempt {attempt} for user {user_id}: {e}")
                print(f"[consume_credit] Supabase RPC error on attempt {attempt} for user {user_id}: {e}", flush=True)
                if attempt < max_retries:
                    await asyncio.sleep(2 ** attempt) # Exponential backoff
                    continue
                else:
                    logger.error(f"[consume_credit] Failed to decrement credits after {max_retries} attempts for user {user_id}.")
                    return

        except Exception as e:
            logger.error(f"[consume_credit] Unexpected error during credit consumption on attempt {attempt} for user {user_id}: {e}", exc_info=True)
            print(f"[consume_credit] Unexpected error during credit consumption on attempt {attempt} for user {user_id}: {e}", flush=True)
            if attempt < max_retries:
                await asyncio.sleep(2 ** attempt) # Exponential backoff
                continue
            else:
                logger.error(f"[consume_credit] Failed due to unexpected error after {max_retries} attempts for user {user_id}.")
                return


async def process_request_without_file(request: Request, user_id: str, api_key: str, query: str) -> str:
    """
    Process a legal request without a file attachment using the OpenAI API.
    
    Args:
        request: The FastAPI request object
        user_id: The user ID making the request
        api_key: The API key for authentication
        query: The user's question
        
    Returns:
        str: The AI-generated response
    """
    logger.info(f"Processing request without file for user {user_id}")
    
    # Verify user_id and API key
    user_id = await check_user_key(request, user_id, api_key)
    
    # Use the prompt template for requests without files
    prompt = PROMPT_TEMPLATE.substitute(context="", question=query)
    logger.debug(f"Generated prompt: {prompt[:500]}...")
    
    # Create the message content for OpenAI
    user_message_content = [{"type": "text", "text": prompt}]
    
    # Query the OpenAI API
    response_text = await query_openai(request, user_message_content)
    logger.debug(f"AI response: {response_text}")
    
    return response_text


async def process_request_with_file(request: Request, user_id: str, api_key: str, query: str, file: UploadFile) -> str:
    """
    Process a legal request with a file attachment using the Gemini API.
    
    Args:
        request: The FastAPI request object
        user_id: The user ID making the request
        api_key: The API key for authentication
        query: The user's question
        file: The uploaded file
        
    Returns:
        str: The AI-generated response
    """
    logger.info(f"Processing request with file for user {user_id}")
    
    # Verify user_id and API key first
    user_id = await check_user_key(request, user_id, api_key)
    
    try:
        file_content = await file.read()
    except Exception as e:
        logger.error(f"Failed to read uploaded file: {e}")
        raise HTTPException(status_code=400, detail="Could not read the uploaded file")

    file_type = file.content_type
    if file_type not in ALLOWED_FILE_TYPES:
        raise HTTPException(
            status_code=400,
            detail=f"Unsupported file type: {file_type}. Supported types are: {', '.join(ALLOWED_FILE_TYPES)}"
        )

    # Enhanced file validation using signature validation and content checks
    try:
        # First, validate file signature (magic numbers)
        if not validate_file_signature(file_content, file_type):
            raise HTTPException(
                status_code=400,
                detail=f"Invalid file format: File signature does not match expected {file_type} format. Please ensure the file is not corrupted or mislabeled."
            )

        # Additional content validation for specific file types
        if file_type == "text/plain":
            # For text files, ensure content is valid UTF-8
            file_content.decode('utf-8')
            logger.debug("Text file validation passed")
        elif file_type == "application/pdf":
            logger.debug("PDF file signature validation passed")
        elif file_type in IMAGE_FILE_TYPES:
            logger.debug(f"Image file signature validation passed for {file_type}")

    except UnicodeDecodeError as e:
        logger.warning(f"File content contains non-UTF-8 data for {file_type} file: {e}")
        raise HTTPException(
            status_code=400,
            detail="Invalid text file: File contains non-UTF-8 characters. Please ensure the file is encoded in UTF-8 format."
        )
    except HTTPException:
        # Re-raise HTTPExceptions from validation
        raise
    except Exception as e:
        logger.warning(f"File validation failed for {file_type}: {e}")
        raise HTTPException(
            status_code=400,
            detail=f"Invalid file format: Could not validate {file_type} file. Please ensure the file is not corrupted."
        )

    if file_type in IMAGE_FILE_TYPES:
        # Handle image file
        logger.info(f"Processing uploaded image ({file_type})")
        base64_image = base64.b64encode(file_content).decode("utf-8")
        user_message_content = [{"type": "text", "text": query}]
        user_message_content.append({
            "type": "image_url",
            "image_url": {"url": f"data:{file_type};base64,{base64_image}"}
        })
        
        # Pass structured message to query_openai
        response_text = await query_openai(request, user_message_content)
        return response_text

    elif file_type in ["application/pdf", "text/plain"]:
        # Handle document files using Gemini Files API
        logger.info(f"Processing uploaded document ({file_type})")
        file_uri = await upload_file_to_gemini(file_content, file.filename or "uploaded_file", file_type)
        file_context = f"File uploaded to Gemini: {file_uri}"
        logger.info(f"File uploaded successfully: {file_uri}")
        
        # Use the file analysis prompt template for requests with files
        prompt = PROMPT_FILE_ANALYSIS.substitute(context=file_context, question=query, filename=file.filename or "uploaded_file")
        logger.debug(f"Generated prompt: {prompt[:500]}...")
        
        # Create the message content for OpenAI/Gemini
        user_message_content = [{"type": "text", "text": prompt}]
        
        # Pass structured message to query_openai
        response_text = await query_openai(request, user_message_content)
        return response_text

@app.post("/legal")
async def legal_endpoint(
    request: Request,
    background_tasks: BackgroundTasks,
):
    """
    Endpoint to handle legal requests, with optional file upload for context.
    Accepts both multipart/form-data and application/json.
    """
    start_time = time.time()
    client_host = request.client.host if request.client else "unknown"
    access_logger.info(f"Request from {client_host} to /legal endpoint")
    
    try:
        content_type = request.headers.get("content-type", "")
        
        user_id = None
        api_key = None
        query = None
        file = None

        # Handle JSON requests
        if "application/json" in content_type:
            try:
                json_body = await request.json()
                data = LegalRequestData(**json_body)
                user_id = data.user_id
                api_key = data.api_key
                query = data.query
            except json.JSONDecodeError:
                raise HTTPException(status_code=400, detail="Invalid JSON body")
            except Exception: # Catches Pydantic validation errors
                raise HTTPException(status_code=422, detail="Invalid request body")

        # Handle multipart/form-data requests
        elif "multipart/form-data" in content_type:
            form_data = await request.form()
            user_id = form_data.get("user_id")
            api_key = form_data.get("api_key")
            query = form_data.get("query")
            file = form_data.get("file")
            if not all([user_id, api_key, query]):
                raise HTTPException(status_code=400, detail="Missing form fields: user_id, api_key, query")
        
        else:
            raise HTTPException(status_code=415, detail=f"Unsupported content type: {content_type}")

        # Dispatch to the appropriate function
        if file and isinstance(file, UploadFile) and file.filename:
            response_text = await process_request_with_file(request, user_id, api_key, query, file)
        else:
            response_text = await process_request_without_file(request, user_id, api_key, query)

        # Add credit consumption to background tasks
        background_tasks.add_task(consume_credit, request, user_id)

        processing_time = time.time() - start_time
        logger.info(f"Legal request processed successfully in {processing_time:.2f} seconds for user {user_id}")
        access_logger.info(f"Request from {client_host} completed in {processing_time:.2f}s with status 200")
        return {"response": response_text}

    except HTTPException as e:
        logger.error(f"HTTP Exception: Status={e.status_code}, Detail={e.detail}")
        access_logger.info(f"Request from {client_host} failed with status {e.status_code}")
        print(f"VERCEL_ERROR_LOG: HTTPException {e.status_code} - {e.detail}", flush=True)
        e.detail = sanitize_error_detail(e.detail)
        raise e
    except Exception as e:
        logger.error(f"Unexpected Exception: {e}", exc_info=True)
        access_logger.info(f"Request from {client_host} failed with status 500")
        print(f"VERCEL_ERROR_LOG: Unexpected Exception - {e}", flush=True)
        safe_detail = "Internal server error occurred during request processing"
        raise HTTPException(status_code=500, detail=safe_detail)

@app.get("/status", status_code=HTTPStatus.OK)
async def get_status():
    """Simple health check endpoint."""
    logger.info("Health check endpoint /status accessed")
    return {"status": "ok"}

# Add a root endpoint for basic testing
@app.get("/")
async def read_root():
    logger.info("Root endpoint / accessed")
    return {"message": "API is running"}

@app.get("/health")
async def health():
    # Check if Supabase client is initialized
    if not hasattr(app.state, 'supabase') or app.state.supabase is None:
        return {
            "status": "error",
            "detail": "Supabase client not initialized."
        }
    # Check if OpenAI clients are initialized
    if not hasattr(app.state, 'openai_clients') or not app.state.openai_clients:
        return {
            "status": "error",
            "detail": "OpenAI clients not initialized."
        }
    return {"status": "ok"}

# Example endpoint using supabase
@app.get("/some-endpoint")
async def some_endpoint():
    if not hasattr(app.state, 'supabase') or app.state.supabase is None:
        raise HTTPException(
            status_code=500,
            detail={
                "error": "Authentication service not initialized",
                "init_error": "Supabase client not found in app state."
            }
        )
    try:
        # Example: fetch data
        # Note: Replace "your_table" with an actual table name in your Supabase project
        data = await app.state.supabase.from_("profiles").select("*").execute()
        return data.data
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail={
                "error": "Supabase query failed",
                "exception": str(e)
            }
        )

# Run initialization
@app.on_event("startup")
async def startup_event():
    await initialize_app_state()
