"""
Question Processing Mo<PERSON><PERSON> - <PERSON>les legal questions without file attachments.

This module contains the functionality to process legal questions using the OpenAI API
without any file attachments. It includes user authentication, prompt generation,
and AI query processing.
"""

import logging
import os
from http import HTTPStatus
from pathlib import Path
from string import Template
from typing import List

import httpx
from fastapi import HTTPException, Request
from openai import AsyncOpenAI, OpenAIError
import openai

# Initialize logger
logger = logging.getLogger(__name__)

# Load environment variables
SUPABASE_URL: str = os.getenv("SUPABASE_URL")
SUPABASE_KEY: str = os.getenv("SUPABASE_KEY")
gemini_model: str = os.getenv("GEMINI_MODEL", "gemini-2.0-flash")

# Initialize headers for Supabase
SUPABASE_HEADERS = {
    "apikey": str(SUPABASE_KEY),
    "Authorization": f"Bearer {SUPABASE_KEY}",
    "Content-Type": "application/json"
}

# Load prompt template
def load_prompt_template() -> Template:
    """Load the prompt template for questions without files."""
    script_dir = Path(__file__).parent
    prompt_template_path = script_dir / "prompt_template.pt"
    try:
        with open(prompt_template_path, "r", encoding="utf-8") as f:
            template = Template(f.read())
        logger.info(f"Prompt template loaded successfully from {prompt_template_path}")
        return template
    except FileNotFoundError:
        logger.error(f"Prompt template file not found at {prompt_template_path}")
        raise FileNotFoundError(f"Required prompt template file not found: {prompt_template_path}")
    except Exception as e:
        logger.error(f"Error loading prompt template file {prompt_template_path}: {e}", exc_info=True)
        raise

# Initialize the prompt template
PROMPT_TEMPLATE = load_prompt_template()


async def check_user_key(request: Request, user_id: str, api_key: str) -> str:
    """
    Verifies the provided user_id and API key using Supabase REST API.
    
    Args:
        request: The FastAPI request object
        user_id: The user ID to verify
        api_key: The API key to verify
        
    Returns:
        str: The verified user_id
        
    Raises:
        HTTPException: If authentication fails or user has insufficient credits
    """
    async with httpx.AsyncClient() as client:
        try:
            logger.debug(f"Querying Supabase profiles for user_id={user_id}")
            response = await client.get(
                f"{SUPABASE_URL}/rest/v1/profiles",
                headers=SUPABASE_HEADERS,
                params={
                    "user_id": f"eq.{user_id}",
                    "select": "api_key,credits"
                }
            )
            logger.debug(f"Supabase response status: {response.status_code}, body: {response.text}")

            if response.status_code != 200:
                logger.error(f"Supabase REST API error: {response.status_code} - {response.text}")
                raise HTTPException(
                    status_code=500,
                    detail={
                        "error": "Failed to fetch user profile",
                        "status": response.status_code
                    }
                )

            profiles = response.json()
            if not profiles:
                logger.error(f"No profile found for user_id={user_id}. Supabase response: {response.text}")
                raise HTTPException(status_code=404, detail=f"User profile not found for user_id={user_id}")

            profile = profiles[0]
            if profile["api_key"] != api_key:
                raise HTTPException(status_code=403, detail="Invalid API key")
            
            if profile["credits"] <= 0:
                raise HTTPException(status_code=403, detail="Insufficient credits")

            return user_id

        except httpx.RequestError as e:
            logger.error(f"HTTP request error: {e}")
            raise HTTPException(
                status_code=500,
                detail={
                    "error": "Failed to connect to authentication service",
                    "detail": str(e)
                }
            )


async def query_openai(request: Request, user_message_content: List[dict]) -> str:
    """
    Queries the configured Generative AI model with a structured message, key rotation, and error handling.
    
    Args:
        request: The FastAPI request object
        user_message_content: The structured message content for the AI model
        
    Returns:
        str: The AI-generated response
        
    Raises:
        HTTPException: If the AI service is unavailable or returns an error
    """
    openai_clients = getattr(request.app.state, "openai_clients", [])
    
    if not openai_clients:
        error_msg = "No OpenAI clients initialized in application state."
        logger.error(error_msg)
        raise HTTPException(
            status_code=500,
            detail={
                "error": error_msg,
                "reason": "Application initialization incomplete",
                "fix": "Verify environment variables (LLM_API_KEY, LLM_API_KEY1) configuration"
            }
        )

    num_clients = len(openai_clients)
    start_index = getattr(request.app.state, "openai_client_index", 0)
    
    # Try all available clients, starting from the current index
    for i in range(num_clients):
        client_index = (start_index + i) % num_clients
        client = openai_clients[client_index]

        try:
            logger.debug(f"Sending message to model {gemini_model} using client (index {client_index})")
            response = await client.chat.completions.create(
                model=gemini_model,
                messages=[
                    {"role": "system", "content": "Tu es un expert en droit français, spécialisé dans les questions juridiques et légales. Tu apportes des réponses claires, précises et fiables, en t'appuyant sur la législation française en vigueur, la jurisprudence pertinente et les principes fondamentaux du droit."},
                    {"role": "user", "content": user_message_content}
                ],
                n=1
            )
            response_text = response.choices[0].message.content
            if not response_text:
                logger.warning(f"Received empty response from AI model using client index {client_index}.")
                continue

            logger.debug(f"Received response from client index {client_index}: {response_text[:200]}...")
            
            # On success, update the index for the *next* request and return
            request.app.state.openai_client_index = (client_index + 1) % num_clients
            return response_text

        except (openai.RateLimitError, openai.APIConnectionError, openai.AuthenticationError) as e:
            logger.warning(f"OpenAI client (index {client_index}) failed with {type(e).__name__}. Trying next client. Error: {e}")
            if i == num_clients - 1:  # This was the last client to try
                logger.error(f"All OpenAI clients failed. Last error from client {client_index}: {e}")
                # Raise a specific error based on the last exception type
                if isinstance(e, openai.RateLimitError):
                    raise HTTPException(
                        status_code=HTTPStatus.TOO_MANY_REQUESTS,
                        detail="AI model rate limit exceeded for all available keys. Please try again later."
                    )
                if isinstance(e, openai.AuthenticationError):
                    raise HTTPException(
                        status_code=HTTPStatus.UNAUTHORIZED,
                        detail="AI service authentication failed for all available keys. Check API keys."
                    )
                raise HTTPException(
                    status_code=HTTPStatus.SERVICE_UNAVAILABLE,
                    detail="Could not connect to the AI model service with any available key."
                )
        except OpenAIError as e:
            logger.error(f"OpenAI API error for client (index {client_index}): {e} (Type: {type(e).__name__})")
            raise HTTPException(status_code=500, detail=f"Error querying AI model: {e}")
        except Exception as e:
            logger.error(f"Unexpected error querying AI model with client (index {client_index}): {e}", exc_info=True)
            raise HTTPException(status_code=500, detail=f"Unexpected internal server error: {e}")

    # If the loop finishes without returning, it means all clients failed
    logger.error("All OpenAI clients failed to process the request.")
    raise HTTPException(status_code=500, detail="AI model returned an empty response from all available keys.")


async def process_request_without_file(request: Request, user_id: str, api_key: str, query: str) -> str:
    """
    Process a legal request without a file attachment using the OpenAI API.
    
    Args:
        request: The FastAPI request object
        user_id: The user ID making the request
        api_key: The API key for authentication
        query: The user's question
        
    Returns:
        str: The AI-generated response
        
    Raises:
        HTTPException: If authentication fails or AI service is unavailable
    """
    logger.info(f"Processing request without file for user {user_id}")
    
    # Verify user_id and API key
    user_id = await check_user_key(request, user_id, api_key)
    
    # Use the prompt template for requests without files
    prompt = PROMPT_TEMPLATE.substitute(context="", question=query)
    logger.debug(f"Generated prompt: {prompt[:500]}...")
    
    # Create the message content for OpenAI
    user_message_content = [{"type": "text", "text": prompt}]
    
    # Query the OpenAI API
    response_text = await query_openai(request, user_message_content)
    logger.debug(f"AI response: {response_text}")
    
    return response_text
