"""
File Processing Module - Handles legal questions with file attachments.

This module contains the functionality to process legal questions with file attachments
using the Gemini API. It includes file validation, upload to Gemini Files API,
user authentication, and AI query processing.
"""

import base64
import io
import logging
import os
import tempfile
from pathlib import Path
from string import Template
from typing import List

import httpx
import pypdf
from fastapi import HTTPException, Request, UploadFile
from openai import Async<PERSON>penA<PERSON>, OpenAIError
import openai
from http import HTTPStatus

import google.generativeai as genai
from google.api_core.exceptions import ResourceExhausted

# Initialize logger
logger = logging.getLogger(__name__)

# Load environment variables
SUPABASE_URL: str = os.getenv("SUPABASE_URL")
SUPABASE_KEY: str = os.getenv("SUPABASE_KEY")
gemini_model: str = os.getenv("GEMINI_MODEL", "gemini-2.0-flash")

# Initialize headers for Supabase
SUPABASE_HEADERS = {
    "apikey": str(SUPABASE_KEY),
    "Authorization": f"Bearer {SUPABASE_KEY}",
    "Content-Type": "application/json"
}

# File type constants
ALLOWED_FILE_TYPES = {
    "application/pdf",
    "text/plain",
    "image/png",
    "image/jpeg",
    "image/webp",
}
IMAGE_FILE_TYPES = {"image/png", "image/jpeg", "image/webp"}

# Load file analysis prompt template
def load_file_analysis_template() -> Template:
    """Load the prompt template for file analysis."""
    script_dir = Path(__file__).parent
    prompt_file_analysis_path = script_dir / "prompt_file_analysis.pt"
    try:
        with open(prompt_file_analysis_path, "r", encoding="utf-8") as f:
            template = Template(f.read())
        logger.info(f"File analysis prompt template loaded successfully from {prompt_file_analysis_path}")
        return template
    except FileNotFoundError:
        logger.error(f"File analysis prompt template file not found at {prompt_file_analysis_path}")
        raise FileNotFoundError(f"Required file analysis prompt template file not found: {prompt_file_analysis_path}")
    except Exception as e:
        logger.error(f"Error loading file analysis prompt template file {prompt_file_analysis_path}: {e}", exc_info=True)
        raise

# Initialize the file analysis prompt template
PROMPT_FILE_ANALYSIS = load_file_analysis_template()


def validate_file_signature(file_content: bytes, expected_mime_type: str) -> bool:
    """
    Validates file content against expected MIME type by checking file signatures (magic numbers).
    Returns True if the file signature matches the expected type, False otherwise.
    """
    if not file_content:
        return False

    # File signature mappings
    signatures = {
        "application/pdf": [b'%PDF-'],
        "text/plain": [],  # Text files don't have reliable magic numbers, validated separately
        "image/png": [b'\x89PNG\r\n\x1a\n'],
        "image/jpeg": [b'\xff\xd8'],
        "image/webp": [b'RIFF'],
    }

    expected_signatures = signatures.get(expected_mime_type, [])
    if not expected_signatures:
        # For types without specific signatures (like text/plain), we'll rely on other validation
        return True

    # Check if file starts with any of the expected signatures
    for signature in expected_signatures:
        if file_content.startswith(signature):
            # Additional validation for WebP (needs to check RIFF header + WEBP)
            if expected_mime_type == "image/webp":
                if len(file_content) >= 12 and file_content[8:12] == b'WEBP':
                    return True
            else:
                return True

    return False


async def extract_text_from_pdf(file_content: bytes) -> str:
    """Extracts text from a PDF file's content."""
    try:
        pdf_reader = pypdf.PdfReader(io.BytesIO(file_content))
        text = ""
        for page in pdf_reader.pages:
            page_text = page.extract_text()
            if page_text:
                text += page_text + "\n"
        return text
    except Exception as e:
        logger.error(f"Failed to extract text from PDF: {e}", exc_info=True)
        raise HTTPException(status_code=400, detail="Could not process the PDF file. It may be corrupt or unreadable.")


async def upload_file_to_gemini(request: Request, file_content: bytes, filename: str, content_type: str) -> str:
    """
    Uploads a file to Gemini Files API and returns the file URI.
    """
    verified_keys = getattr(request.app.state, "verified_gemini_keys", [])
    if not verified_keys:
        logger.error("No verified Gemini API keys available in application state.")
        raise HTTPException(status_code=500, detail="No verified Gemini API keys available.")

    # Determine MIME type for Gemini
    mime_type = content_type
    if content_type == "text/plain":
        mime_type = "text/plain"

    num_keys = len(verified_keys)
    start_index = getattr(request.app.state, "gemini_key_index", 0)

    for i in range(num_keys):
        key_index = (start_index + i) % num_keys
        current_key = verified_keys[key_index]

        try:
            genai.configure(api_key=current_key)
            # Create a temporary file and write the content to it
            with tempfile.NamedTemporaryFile(delete=False, suffix=Path(filename).suffix) as temp_file:
                temp_file.write(file_content)
                temp_file_path = temp_file.name

            try:
                # Upload file to Gemini
                file_obj = genai.upload_file(path=temp_file_path, display_name=filename, mime_type=mime_type)
                logger.info(f"File uploaded to Gemini: {filename}, URI: {file_obj.uri} using key index {key_index}")
                # On success, update the key index for the next request
                request.app.state.gemini_key_index = (key_index + 1) % num_keys
                return file_obj.uri
            finally:
                # Ensure the temporary file is deleted
                os.remove(temp_file_path)

        except ResourceExhausted as e:
            logger.warning(f"Gemini key index {key_index} hit rate limit: {e}. Trying next key.")
            if i == num_keys - 1:
                logger.error("All verified Gemini keys hit rate limit.")
                raise HTTPException(
                    status_code=429,
                    detail="All available Gemini API keys have hit rate limits. Please try again later."
                )
            # Continue to next key

        except Exception as e:
            logger.error(f"Failed to upload file to Gemini with key index {key_index}: {e}", exc_info=True)
            # For other exceptions or errors, raise immediately without trying other keys
            raise HTTPException(status_code=500, detail="Failed to upload file to Gemini Files API.")


async def check_user_key(request: Request, user_id: str, api_key: str) -> str:
    """
    Verifies the provided user_id and API key using Supabase REST API.
    
    Args:
        request: The FastAPI request object
        user_id: The user ID to verify
        api_key: The API key to verify
        
    Returns:
        str: The verified user_id
        
    Raises:
        HTTPException: If authentication fails or user has insufficient credits
    """
    async with httpx.AsyncClient() as client:
        try:
            logger.debug(f"Querying Supabase profiles for user_id={user_id}")
            response = await client.get(
                f"{SUPABASE_URL}/rest/v1/profiles",
                headers=SUPABASE_HEADERS,
                params={
                    "user_id": f"eq.{user_id}",
                    "select": "api_key,credits"
                }
            )
            logger.debug(f"Supabase response status: {response.status_code}, body: {response.text}")

            if response.status_code != 200:
                logger.error(f"Supabase REST API error: {response.status_code} - {response.text}")
                raise HTTPException(
                    status_code=500,
                    detail={
                        "error": "Failed to fetch user profile",
                        "status": response.status_code
                    }
                )

            profiles = response.json()
            if not profiles:
                logger.error(f"No profile found for user_id={user_id}. Supabase response: {response.text}")
                raise HTTPException(status_code=404, detail=f"User profile not found for user_id={user_id}")

            profile = profiles[0]
            if profile["api_key"] != api_key:
                raise HTTPException(status_code=403, detail="Invalid API key")
            
            if profile["credits"] <= 0:
                raise HTTPException(status_code=403, detail="Insufficient credits")

            return user_id

        except httpx.RequestError as e:
            logger.error(f"HTTP request error: {e}")
            raise HTTPException(
                status_code=500,
                detail={
                    "error": "Failed to connect to authentication service",
                    "detail": str(e)
                }
            )


async def query_openai(request: Request, user_message_content: List[dict]) -> str:
    """
    Queries the configured Generative AI model with a structured message, key rotation, and error handling.
    
    Args:
        request: The FastAPI request object
        user_message_content: The structured message content for the AI model
        
    Returns:
        str: The AI-generated response
        
    Raises:
        HTTPException: If the AI service is unavailable or returns an error
    """
    openai_clients = getattr(request.app.state, "openai_clients", [])
    
    if not openai_clients:
        error_msg = "No OpenAI clients initialized in application state."
        logger.error(error_msg)
        raise HTTPException(
            status_code=500,
            detail={
                "error": error_msg,
                "reason": "Application initialization incomplete",
                "fix": "Verify environment variables (LLM_API_KEY, LLM_API_KEY1) configuration"
            }
        )

    num_clients = len(openai_clients)
    start_index = getattr(request.app.state, "openai_client_index", 0)
    
    # Try all available clients, starting from the current index
    for i in range(num_clients):
        client_index = (start_index + i) % num_clients
        client = openai_clients[client_index]

        try:
            logger.debug(f"Sending message to model {gemini_model} using client (index {client_index})")
            response = await client.chat.completions.create(
                model=gemini_model,
                messages=[
                    {"role": "system", "content": "Tu es un expert en droit français, spécialisé dans les questions juridiques et légales. Tu apportes des réponses claires, précises et fiables, en t'appuyant sur la législation française en vigueur, la jurisprudence pertinente et les principes fondamentaux du droit."},
                    {"role": "user", "content": user_message_content}
                ],
                n=1
            )
            response_text = response.choices[0].message.content
            if not response_text:
                logger.warning(f"Received empty response from AI model using client index {client_index}.")
                continue

            logger.debug(f"Received response from client index {client_index}: {response_text[:200]}...")
            
            # On success, update the index for the *next* request and return
            request.app.state.openai_client_index = (client_index + 1) % num_clients
            return response_text

        except (openai.RateLimitError, openai.APIConnectionError, openai.AuthenticationError) as e:
            logger.warning(f"OpenAI client (index {client_index}) failed with {type(e).__name__}. Trying next client. Error: {e}")
            if i == num_clients - 1:  # This was the last client to try
                logger.error(f"All OpenAI clients failed. Last error from client {client_index}: {e}")
                # Raise a specific error based on the last exception type
                if isinstance(e, openai.RateLimitError):
                    raise HTTPException(
                        status_code=HTTPStatus.TOO_MANY_REQUESTS,
                        detail="AI model rate limit exceeded for all available keys. Please try again later."
                    )
                if isinstance(e, openai.AuthenticationError):
                    raise HTTPException(
                        status_code=HTTPStatus.UNAUTHORIZED,
                        detail="AI service authentication failed for all available keys. Check API keys."
                    )
                raise HTTPException(
                    status_code=HTTPStatus.SERVICE_UNAVAILABLE,
                    detail="Could not connect to the AI model service with any available key."
                )
        except OpenAIError as e:
            logger.error(f"OpenAI API error for client (index {client_index}): {e} (Type: {type(e).__name__})")
            raise HTTPException(status_code=500, detail=f"Error querying AI model: {e}")
        except Exception as e:
            logger.error(f"Unexpected error querying AI model with client (index {client_index}): {e}", exc_info=True)
            raise HTTPException(status_code=500, detail=f"Unexpected internal server error: {e}")

    # If the loop finishes without returning, it means all clients failed
    logger.error("All OpenAI clients failed to process the request.")
    raise HTTPException(status_code=500, detail="AI model returned an empty response from all available keys.")


async def process_request_with_file(request: Request, user_id: str, api_key: str, query: str, file: UploadFile) -> str:
    """
    Process a legal request with a file attachment using the Gemini API.

    Args:
        request: The FastAPI request object
        user_id: The user ID making the request
        api_key: The API key for authentication
        query: The user's question
        file: The uploaded file

    Returns:
        str: The AI-generated response

    Raises:
        HTTPException: If authentication fails, file is invalid, or AI service is unavailable
    """
    logger.info(f"Processing request with file for user {user_id}")

    # Verify user_id and API key first
    user_id = await check_user_key(request, user_id, api_key)

    try:
        file_content = await file.read()
    except Exception as e:
        logger.error(f"Failed to read uploaded file: {e}")
        raise HTTPException(status_code=400, detail="Could not read the uploaded file")

    file_type = file.content_type
    if file_type not in ALLOWED_FILE_TYPES:
        raise HTTPException(
            status_code=400,
            detail=f"Unsupported file type: {file_type}. Supported types are: {', '.join(ALLOWED_FILE_TYPES)}"
        )

    # Enhanced file validation using signature validation and content checks
    try:
        # First, validate file signature (magic numbers)
        if not validate_file_signature(file_content, file_type):
            raise HTTPException(
                status_code=400,
                detail=f"Invalid file format: File signature does not match expected {file_type} format. Please ensure the file is not corrupted or mislabeled."
            )

        # Additional content validation for specific file types
        if file_type == "text/plain":
            # For text files, ensure content is valid UTF-8
            file_content.decode('utf-8')
            logger.debug("Text file validation passed")
        elif file_type == "application/pdf":
            logger.debug("PDF file signature validation passed")
        elif file_type in IMAGE_FILE_TYPES:
            logger.debug(f"Image file signature validation passed for {file_type}")

    except UnicodeDecodeError as e:
        logger.warning(f"File content contains non-UTF-8 data for {file_type} file: {e}")
        raise HTTPException(
            status_code=400,
            detail="Invalid text file: File contains non-UTF-8 characters. Please ensure the file is encoded in UTF-8 format."
        )
    except HTTPException:
        # Re-raise HTTPExceptions from validation
        raise
    except Exception as e:
        logger.warning(f"File validation failed for {file_type}: {e}")
        raise HTTPException(
            status_code=400,
            detail=f"Invalid file format: Could not validate {file_type} file. Please ensure the file is not corrupted."
        )

    if file_type in IMAGE_FILE_TYPES:
        # Handle image file
        logger.info(f"Processing uploaded image ({file_type})")
        base64_image = base64.b64encode(file_content).decode("utf-8")
        user_message_content = [{"type": "text", "text": query}]
        user_message_content.append({
            "type": "image_url",
            "image_url": {"url": f"data:{file_type};base64,{base64_image}"}
        })

        # Pass structured message to query_openai
        response_text = await query_openai(request, user_message_content)
        return response_text

    elif file_type in ["application/pdf", "text/plain"]:
        # Handle document files using Gemini Files API
        logger.info(f"Processing uploaded document ({file_type})")
        file_uri = await upload_file_to_gemini(request, file_content, file.filename or "uploaded_file", file_type)
        file_context = f"File uploaded to Gemini: {file_uri}"
        logger.info(f"File uploaded successfully: {file_uri}")

        # Use the file analysis prompt template for requests with files
        prompt = PROMPT_FILE_ANALYSIS.substitute(context=file_context, question=query, filename=file.filename or "uploaded_file")
        logger.debug(f"Generated prompt: {prompt[:500]}...")

        # Create the message content for OpenAI/Gemini
        user_message_content = [{"type": "text", "text": prompt}]

        # Pass structured message to query_openai
        response_text = await query_openai(request, user_message_content)
        return response_text
