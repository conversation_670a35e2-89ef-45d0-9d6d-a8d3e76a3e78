{"version": 2, "env": {"SUPABASE_URL": "https://kwtpkihipcokqbrfvxhn.supabase.co", "SUPABASE_KEY": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imt3dHBraWhpcGNva3FicmZ2eGhuIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc0MTAyNzcwNywiZXhwIjoyMDU2NjAzNzA3fQ.9a0x65zz_CMGGZRe-akZdDyelFOTZXGjVe7lpsDXW70", "LLM_API_KEY": "AIzaSyBA1s5IGCtTEnlzvjKgHDWfBnr0llzJjd0", "LLM_API_KEY1": "AIzaSyCrmZLS_IiiFpB_VJgUxqN-Swv8dASGclk", "GEMINI_BASE_URL": "https://generativelanguage.googleapis.com/v1beta/openai/", "GEMINI_MODEL": "gemini-2.0-flash", "MAX_REQUESTS_PER_DAY": "1500", "MAX_REQUESTS_PER_MINUTE": "15", "LOG_INFO_ENABLED": "False", "LOG_DEBUG_ENABLED": "True", "LOG_WARNING_ENABLED": "False", "LOG_ERROR_ENABLED": "True"}, "builds": [{"src": "api/main.py", "use": "@vercel/python"}], "routes": [{"src": "/(.*)", "headers": {"Access-Control-Allow-Origin": "*", "Access-Control-Allow-Methods": "*", "Access-Control-Allow-Headers": "*"}, "dest": "api/main.py"}]}