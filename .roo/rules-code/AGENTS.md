# Project Code Rules (Non-Obvious Only)

- **API Key Confusion Risk**: LLM_API_KEY/LLM_API_KEY1 are Google Gemini keys (not OpenAI) - this confuses developers reading the code
- **Dual import Pattern**: Both `openai` and `google.generativeai` imported for same API key usage - unusual but intentional
- **Thread Safety Considerations**: `genai.configure()` modifies global state - documented but easy to overlook in concurrent environments
- **ResourceExhausted Exception**: Google-specific exception handling - not standard HTTP 429 handling like other APIs
- **File Processing Dependencies**: pypdf imported with warning - indicates brittle dependency discovered through testing
- **Vercel Deployment Secrets**: vercel.json contains actual API keys (committed accidentally) - security anti-pattern but functional
- **Dedicated Import Sections**: Very organized import structure with clear sections (stdlib, third-party, local) maintained consistently