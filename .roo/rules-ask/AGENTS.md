# Project Documentation Rules (Non-Obvious Only)

- **API Key Ambiguity**: LLM_API_KEY/LLM_API_KEY1 are Google Gemini keys despite OpenAI naming - this confuses developers
- **Gemini Dual Interface**: Same Google API keys used for both OpenAI-compatible chat AND native Gemini Files API
- **French Legal Templates**: Specialized legal analysis templates in api/prompt_file_analysis.pt require French law knowledge
- **Supabase RLS Confusion**: Service role key silently required for admin operations - anon key appears to work but fails on writes
- **ASGI Startup Critical**: Vercel deployment crashes without FastAPI lifespan events - explains "AI service not initialized" error
- **File Processing Flow**: Files upload to Gemini Files API first, then analyzed via URI - not direct content processing
- **Rate Limiting Behavior**: 429 errors automatically retry with next key, failures only logged server-side
- **Environment Flexibility**: GEMINI_API_KEY optional if LLM keys exist, providing bilateral API coverage