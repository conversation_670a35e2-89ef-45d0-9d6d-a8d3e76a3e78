# Project Architecture Rules (Non-Obvious Only)

- **Supabase RLS Requirement**: Service role key required for reading/writing profiles table - anon key fails silently on admin operations
- **Gemini API Key Hierarchy**: GEMINI_API_KEY → LLM_API_KEY → LLM_API_KEY1 with automatic rotation on ResourceExhausted
- **File Processing Architecture**: Upload files to Gemini Files API first, generate URI, then query with analysis templates
- **French Legal Specialization**: Custom prompt templates in api/ require French legal domain expertise
- **Vercel Deployment Architecture**: ASGI server required - lifespan events initialize OpenAI and validate Gemini keys
- **Multi-Client Pattern**: Separate AsyncOpenAI clients for chat + google.generativeai module for file uploads using same keys
- **Global State Thread Safety**: genai.configure() modifies global state - configured once at startup, used across requests
- **API Key Fallback Logic**: GEMINI_API_KEY optional if at least one LLM key provided (LLM keys provide dual API coverage)