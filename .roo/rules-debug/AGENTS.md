# Project Debug Rules (Non-Obvious Only)

- **Cline-rules Integration**: Apply changes one file at a time, allow user review, one-time modifications per file
- **ASGI Lifecycle Debugging**: FastAPI lifespan events must work - verify `uvicorn main:app` startup, not `python main.py`
- **Gemini Key Rotation Debug**: Check key indexes and verify ResourceExhausted exception handling fails over correctly
- **Supabase Connection Debug**: Service role key required for admin operations - anon key silently fails on profiles table writes
- **Thread Safety Gotcha**: `genai.configure()` modifies global state - single-threaded debugging masks concurrency issues
- **File Upload Debug Pattern**: Gemini Files API failures return 500 status - check URI generation and API key validity first
- **Rate Limit Debug**: 429 responses automatically try next key - server logs show which keys fail, not client
- **Environment Validation Debug**: GEMINI_API_KEY optional if LLM keys exist - check collection logic not individual key validation