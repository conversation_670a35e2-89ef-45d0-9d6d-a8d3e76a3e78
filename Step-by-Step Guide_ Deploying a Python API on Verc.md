<img src="https://r2cdn.perplexity.ai/pplx-full-logo-primary-dark%402x.png" class="logo" width="120"/>

## Step-by-Step Guide: Deploying a Python API on Vercel Using the Vercel CLI

**1. Prerequisites**

- Python installed on your machine
- Node.js and npm installed
- Vercel account (free)
- Vercel CLI installed globally:

```
npm i -g vercel
```


**2. Project Structure**

Organize your project directory as follows:

```
your-project/
├── api/
│   └── main.py
├── requirements.txt
└── vercel.json
```

- `main.py`: Your FastAPI or Flask app (example below)
- `requirements.txt`: Python dependencies
- `vercel.json`: Vercel configuration

**3. Write Your Python API**

Example for FastAPI (`api/main.py`):

```python
from fastapi import FastAPI

app = FastAPI()

@app.get("/")
async def health_check():
    return {"message": "Health check successful"}
```

List dependencies in `requirements.txt`:

```
fastapi
uvicorn
```

**4. Configure Vercel**

Create a `vercel.json` file in your project root:

```json
{
  "builds": [
    { "src": "api/main.py", "use": "@vercel/python" }
  ],
  "routes": [
    { "src": "/(.*)", "dest": "api/main.py" }
  ]
}
```

This tells Vercel to use the Python runtime and route all requests to your API handler[^5].

**5. Initialize and Test Locally (Optional)**

- Login to Vercel CLI:

```
vercel login
```

- (Optional) Test locally:

```
vercel dev
```


**6. Deploy to Vercel**

From your project root, run:

```
vercel
```

- Answer the CLI prompts:
    - Set up and deploy? → Yes
    - Select scope (your account)
    - Link to existing project? → No
    - Project name → Choose or accept default
    - Directory location → Enter (root directory)[^5][^9]

Alternatively, for a production deployment:

```
vercel --prod
```

The CLI will upload your files and display a deployment URL when done[^1][^3][^5].

**7. Verify Deployment**

- Visit the deployment URL provided in the CLI output.
- You should see your API live and responding.

---

### Additional Tips

- You can redeploy at any time by running `vercel` or `vercel --prod`.
- For local builds and inspection, use:

```
vercel build
vercel deploy --prebuilt
```

- For Flask or other frameworks, adjust the handler and dependencies accordingly[^6][^7].

---

**Summary Table**


| Step | Command/Action |
| :-- | :-- |
| Install CLI | `npm i -g vercel` |
| Login | `vercel login` |
| Test locally | `vercel dev` |
| Deploy (preview) | `vercel` |
| Deploy (production) | `vercel --prod` |
| Check live URL | Output from CLI |

This process works for FastAPI, Flask, and vanilla Python APIs, provided you configure `vercel.json` and dependencies appropriately[^5][^6][^7].

<div style="text-align: center">⁂</div>

[^1]: https://vercel.com/docs/cli/deploying-from-cli

[^2]: https://www.frontend-devops.com/blog/python-on-vercel

[^3]: https://vercel.com/docs/deployments

[^4]: https://www.youtube.com/watch?v=8R-cetf_sZ4

[^5]: https://substack.com/home/<USER>/p-151460222

[^6]: https://dev.to/andrewbaisden/how-to-deploy-a-python-flask-app-to-vercel-2o5k

[^7]: https://blog.stackademic.com/simple-guide-on-deploying-vanilla-python-api-on-vercel-free-of-cost-be5b6b99f247

[^8]: https://kapsys.io/user-experience/deploying-to-vercel-step-by-step-tutorial

[^9]: https://dev.to/terieyenike/how-to-use-vercel-cli-for-deployment-361f

