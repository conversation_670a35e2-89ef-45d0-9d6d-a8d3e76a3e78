# AGENTS.md

This file provides guidance to agents when working with code in this repository.

## Project-Specific Critical Knowledge (Non-Obvious Only)

- **LLM_API_KEY and LLM_API_KEY1 are Google API keys**: Unlike typical usage, these are Google Gemini API keys (not OpenAI), used for both OpenAI-compatible chat interface and native Gemini Files API
- **Dual Gemini API Usage**: Same API keys support both OpenAI client for text queries AND google.generativeai for file uploads
- **Automatic Key Rotation**: Google API ResourceExhausted (429) exceptions trigger automatic fallback between GEMINI_API_KEY, LLM_API_KEY, and LLM_API_KEY1
- **GEMINI_API_KEY Optional**: Not required if LLM keys are available - they provide fallback coverage for all Gemini operations
- **French Legal Domain**: Project uses specialized French legal analysis templates (api/prompt_file_analysis.pt) for legal document processing
- **File Processing Pipeline**: Files uploaded to Gemini Files API first, generating URI for analysis rather than processing raw content
- **Vercel ASGI Requirement**: MUST use `uvicorn main:app` (not `python main.py`) - FastAPI lifespan events critical for client initialization
- **Supabase Service Role**: SERVICE ROLE key required for admin operations on profiles table (anon key insufficient)
- **Cline Workflow Rules**: Apply changes one file at a time, allow user review, one-time modifications per file

## Build & Development (Non-Standard Commands Only)

**Local Development:**
- `cd api && uvicorn main:app --reload` (server starts on localhost:8000)

**Virtual Environment:**
- `python3 -m venv myenv && source myenv/bin/activate` (setup required)
- Dependencies: `pip install -r requirements.txt`

## Key Environment Variables (Non-Obvious Usage)

- GEMINI_BASE_URL: "https://generativelanguage.googleapis.com/v1beta/openai/" (OpenAI-compatible endpoint for Google Gemini chat)
- GEMINI_MODEL: "gemini-2.0-flash" (model name for the OpenAI-compatible interface)
- LLM_API_KEY / LLM_API_KEY1: Google API keys for BOTH chat queries AND file operations
- GEMINI_API_KEY: Optional fallback key (replaced in production with vercel.json env config)

## Code Architecture Insights

**API Key Hierarchy:**
GEMINI_API_KEY → LLM_API_KEY → LLM_API_KEY1 (rotation on rate limits)

**Dual Client Architecture:**
- AsyncOpenAI client: text queries via OpenAI-compatible interface
- google.module.configure(): file uploads via native Gemini Files API
- Both use same API keys with different endpoints

**File Processing Flow:**
1. Upload to Gemini Files API → get file URI
2. Include file URI in specialized French legal analysis prompt
3. Query Gemini chat interface with document context

## Error Patterns

- `{"detail":"AI service not initialized"}`: ASGI server not running correctly on Vercel
- ResourceExhausted exceptions: handled by key rotation, do not raise user errors
- Rate limit 429: automatically retried with next key, only fail on exhaustion

## Testing Strategy

- No test framework detected - integration testing via health check endpoint
- Manual API endpoint testing recommended for functionality validation

## File Upload Handling

**Supported Types:** PDF, TXT, PNG, JPEG, WebP
**Processing:** Base64 encoding for images, PDF text extraction, direct Gemini Files API upload
**Error Scenarios:** Unsupported formats, corrupted files, API upload failures all handled with appropriate HTTP codes