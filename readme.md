# LegalAPIg

This project is a Python API designed to integrate with Supabase for user management and credits, and with an OpenAPI endpoint for querying data. It features concurrent processing, HTTP/2 support, throttling, and comprehensive logging.

## 🚀 New Feature: Enhanced File Analysis

The latest update introduces advanced document processing capabilities using Google Gemini's Files API:

- **Intelligent PDF Processing**: Automatically analyzes PDF documents with French legal expertise
- **Text File Analysis**: Processes plain text documents with specialized legal templates
- **Optimized AI Integration**: Uses Gemini's native Files API for superior document understanding
- **Specialized Prompts**: French legal templates optimized for document analysis scenarios
- **Seamless Integration**: Maintains existing authentication and credit management while adding file capabilities

## Features

-   **Supabase REST API Integration**: User management and credit tracking using Supabase's REST API endpoints.
-   **Concurrent Processing**: Asynchronous handling of requests for optimal performance.
-   **HTTP/2 Support**: Efficient request handling using FastAPI.
-   **Throttling**: Rate limiting to prevent abuse.
-   **AI Model Integration**: Uses the `openai` library to interact with a configurable generative AI endpoint (e.g., Google Gemini via its OpenAI-compatible endpoint). Dynamic query construction based on user input and templates.
-   **Advanced File Analysis**: Enhanced document processing using Google Gemini's native Files API for PDFs and text files, with specialized French legal analysis templates.
-   **Multiple Prompt Templates**: Optimized prompt templates for standard queries and document analysis scenarios.
-   **Logging**: User request logging and error logging.

## Setup

1. Clone the repository:
    ```bash
    git clone <repository_url>
    ```

2. Navigate to the project directory:
    ```bash
    cd LegalAPIg
    ```

3. Create virtual env:
    ```bash
    python3 -m venv myenv
    source myenv/bin/activate
    ```

4. Install dependencies:
    ```bash
    pip install -r requirements.txt
    ```

5. Create a `.env` file in the `LegalAPIg` directory with the following content:
    ```
    # Supabase REST API Credentials
    SUPABASE_URL="your_supabase_project_url"
    SUPABASE_ANON_KEY="your_supabase_anon_key"
    SUPABASE_SERVICE_ROLE_KEY="your_service_role_key"  # For admin operations

    # AI Model Configuration (using OpenAI library)
    OPENAI_API_KEY="your_api_key_for_the_endpoint" # API key for the target endpoint
    GEMINI_BASE_URL="https://generativelanguage.googleapis.com/v1beta/openai/" # Base URL of the OpenAI-compatible endpoint (defaults to Google Gemini)
    GEMINI_MODEL="gemini-2.0-flash" # Model name to use at the endpoint (defaults to gemini-2.0-flash)

    # Gemini Files API Configuration
    GEMINI_API_KEY="your_gemini_api_key" # API key for Google Gemini Files API (used for enhanced file analysis)
    ```
    Replace the placeholder values with your actual Supabase credentials and the appropriate API key, base URL, and model name for your target AI endpoint.

> **⚠️ Note for Vercel deployment:**  
> If you see the error `{"detail":"AI service not initialized. (Did you start the app with an ASGI server like 'uvicorn main:app'?)"}` in production, it means the FastAPI lifespan events are not running and the OpenAI client is not initialized.  
>  
> - Make sure your `vercel.json` is configured to use an ASGI server (like `uvicorn`) and not to run your script directly.  
> - Your `vercel.json` should have something like:
>   ```json
>  {
    "version": 2,
    "builds": [
        {
            "src": "api/main.py",
            "use": "@vercel/python"
        }
    ],
    "routes": [
        {
            "src": "/(.*)",
            "dest": "api/main.py",
            "headers": {
                "Access-Control-Allow-Origin": "*",
                "Access-Control-Allow-Methods": "GET,POST,OPTIONS",
                "Access-Control-Allow-Headers": "Content-Type,X-Requested-With" 
            }
        }
    ]
}
>   ```
> - If you use a custom server, ensure it starts with `uvicorn main:app` (not just `python main.py`).
> - See [Vercel Python docs](https://vercel.com/docs/frameworks/python) for details.

## Usage

To run the API locally:

```bash
cd api
uvicorn main:app --reload
```

This will start the API server on `http://127.0.0.1:8000`.

## API Endpoints

### `/legal` Endpoint

This endpoint processes legal queries using the configured AI model. Support for file uploads has been added for enhanced document analysis using Google Gemini Files API.

#### Request Examples:

**Text Query (JSON):**
```bash
curl -X POST http://127.0.0.1:8000/legal \
     -H "Content-Type: application/json" \
     -d '{
       "user_id": "564bac7a-60f9-4056-99c7-428ff45d8887",
       "api_key": "872fe3e3-3e92-45dc-9588-8b87e231b344",
       "query": "Comment contester une amende pour stationnement?"
     }'
```

**File with Query (JSON with base64-encoded file):**
```bash
# Method 1: Create JSON file with base64 content (avoids argument list too long error)
base64 -w0 < '/home/<USER>/Desktop/dev/DeployLegaleAPI_File/files/contrat de travail.pdf' > temp_file_b64.txt

cat > request.json << 'EOF'
{
  "user_id": "564bac7a-60f9-4056-99c7-428ff45d8887",
  "api_key": "872fe3e3-3e92-45dc-9588-8b87e231b344",
  "query": "analyse le document",
  "file": "$(cat temp_file_b64.txt)",
  "file_type": "application/pdf"
}
EOF

curl -X POST http://127.0.0.1:8000/legal \
     -H "Content-Type: application/json" \
     --data-binary @request.json

# Clean up temporary files
rm temp_file_b64.txt request.json
```

**Method 2: Using multipart form upload (recommended for large files):**
```bash
curl -X POST http://127.0.0.1:8000/legal \
     -F "user_id=564bac7a-60f9-4056-99c7-428ff45d8887" \
     -F "api_key=872fe3e3-3e92-45dc-9588-8b87e231b344" \
     -F "query=analyse le document" \
     -F "file=@/home/<USER>/Desktop/dev/DeployLegaleAPI_File/files/contrat de travail.pdf;type=application/pdf"
```

**Method 3: Using separate curl command with data from file:**
```bash
# Create the JSON payload file
cat > payload.json << 'EOF'
{
  "user_id": "564bac7a-60f9-4056-99c7-428ff45d8887",
  "api_key": "872fe3e3-3e92-45dc-9588-8b87e231b344",
  "query": "analyse le document",
  "file": "BASE64_CONTENT_HERE",
  "file_type": "application/pdf"
}
EOF

# Replace BASE64_CONTENT_HERE with actual base64 content
base64 -w0 < '/home/<USER>/Desktop/dev/DeployLegaleAPI_File/files/contrat de travail.pdf' | sed 's/BASE64_CONTENT_HERE/'"$(cat)"'/' > final_payload.json

curl -X POST http://127.0.0.1:8000/legal \
     -H "Content-Type: application/json" \
     --data-binary @final_payload.json

# Clean up
rm payload.json final_payload.json
```

**Image with Query (JSON with base64-encoded image):**
```bash
curl -X POST http://127.0.0.1:8000/legal \
     -H "Content-Type: application/json" \
     -d '{
       "user_id": "564bac7a-60f9-4056-99c7-428ff45d8887",
       "api_key": "872fe3e3-3e92-45dc-9588-8b87e231b344",
       "query": "Que représente ce document?",
       "file": "'$(base64 -i /path/to/document.jpeg | tr -d '\n')'",
       "file_type": "image/jpeg"
     }'
```

**Note:** To encode a file as base64 for JSON requests, use:
```bash
base64 -i /path/to/file | tr -d '\n'
```

```bash
# For deployed version

#preview
http://localhost:3000/
deploy-legale-api.vercel.app


curl -X POST https://deploy-legale-api.vercel.app/legal \
     -H "Content-Type: application/json" \
     -d '{
       "user_id": "564bac7a-60f9-4056-99c7-428ff45d8887",
       "api_key": "872fe3e3-3e92-45dc-9588-8b87e231b344",
       "query": "Comment contester une amende pour stationnement?"
     }'


curl -X POST https://qlegale1.vercel.app/legal \
     -H "Content-Type: application/json" \
     -d '{
       "user_id": "564bac7a-60f9-4056-99c7-428ff45d8887",
       "api_key": "872fe3e3-3e92-45dc-9588-8b87e231b344",
       "query": "Comment contester une amende pour stationnement?"
     }'
#prod



# For local development
curl -X POST http://127.0.0.1:8000/legal \
     -H "Content-Type: application/json" \
     -d '{
       "user_id": "564bac7a-60f9-4056-99c7-428ff45d8887",
       "api_key": "872fe3e3-3e92-45dc-9588-8b87e231b344",
       "query": "Comment contester une amende pour stationnement?"
     }'
```

Replace `user_id` with your actual user ID and `api_key` with your API key, and update the query as needed.

**Successful Response (Status Code 200):**

```json
{
  "response": "Pour contester une amende de stationnement, voici les étapes à suivre:\n\n1. Délais:\n- Vous avez 30 jours à partir de la date de l'infraction pour contester\n- Le délai commence à la date inscrite sur l'avis d'infraction\n\n2. Options de contestation:\n- En ligne sur le site de la cour municipale\n- Par la poste en envoyant le formulaire de contestation\n- En personne au bureau de la cour municipale\n\n3. Documents nécessaires:\n- L'avis d'infraction original\n- Preuves justifiant la contestation (photos, reçus, etc.)\n- Pièce d'identité valide\n\n4. Procédure:\n- Remplir le formulaire de plaidoyer de non-culpabilité\n- Joindre les preuves pertinentes\n- Conserver une copie des documents envoyés\n\n5. Suivi:\n- Vous recevrez un avis de convocation pour l'audience\n- Préparez votre défense et vos arguments\n\nImportant: Le dépôt d'une contestation suspend l'obligation de paiement jusqu'au jugement."
}
```

#### File Analysis Features:

The API now supports enhanced document analysis using Google Gemini's Files API:

- **PDF Analysis**: Automatically extracts and analyzes content from PDF documents
- **Text File Analysis**: Processes plain text documents (.txt files)
- **Image Analysis**: Uses base64 encoding for image files (PNG, JPEG, WebP)
- **Specialized Templates**: Uses French legal analysis templates optimized for document analysis
- **File Upload Processing**: Files are uploaded to Gemini Files API for better processing before analysis

**Supported File Types:**
- PDF files (`application/pdf`)
- Text files (`text/plain`)
- Images: PNG, JPEG, WebP (`image/png`, `image/jpeg`, `image/webp`)

**How it Works:**
1. Files are uploaded to Google Gemini Files API
2. A file URI is generated and included in the AI prompt
3. Special file analysis template guides the AI to provide comprehensive document analysis
4. Analysis covers document type, legal provisions, compliance, and actionable recommendations

### `/status` Endpoint

This endpoint provides a simple health check to confirm the API is running.

**Request:**

```bash
# For deployed version
curl https://questionlegale.vercel.app/status

# For local development
curl http://127.0.0.1:8000/status
```

**Successful Response (Status Code 200):**

```json
{
  "status": "ok"
}
```

## Usage Examples

### Supabase REST API Examples

```python
# User Authentication
auth_response = requests.post(
    f"{SUPABASE_URL}/auth/v1/token?grant_type=password",
    headers={
        "apikey": SUPABASE_ANON_KEY,
        "Content-Type": "application/json"
    },
    json={
        "email": "<EMAIL>",
        "password": "password123"
    }
)

# Query Data
response = requests.get(
    f"{SUPABASE_URL}/rest/v1/users",
    headers={
        "apikey": SUPABASE_ANON_KEY,
        "Authorization": f"Bearer {user_token}"
    }
)
```

## Deployment on Vercel

1. Install the Vercel CLI:
    ```bash
    npm install -g vercel
    ```

2. Configure Environment Variables:
   - Go to your Vercel dashboard
   - Select your project
   - Navigate to Settings > Environment Variables
   - Add all the variables from your `.env` file

3. Deploy the project:
    ```bash
    vercel
    ```

4. For production deployment:
    ```bash
    vercel --prod
    ```

The deployment process will automatically:
- Detect your Python project
- Install dependencies from requirements.txt
- Configure the ASGI server for FastAPI
- Set up the routes based on vercel.json

## Project Structure

-   `api/`: Contains the main API code
    - `main.py`: Main FastAPI application with Gemini Files API integration
    - `prompt_template.pt`: Template for standard AI model prompts
    - `prompt_file_analysis.pt`: Specialized template for French legal document analysis
-   `.env`: Environment variables (including GEMINI_API_KEY for Files API)
-   `requirements.txt`: Python dependencies (includes google-generativeai for enhanced file processing)
-   `readme.md`: Project documentation
-   `vercel.json`: Vercel deployment configuration
-   `supabase_profiles_rls.sql`: Supabase RLS policies
-   `supabase_rls_setup.sql`: Supabase setup scripts

## Error Handling

The API includes comprehensive error handling for:
- Invalid/missing credentials
- Insufficient credits
- Rate limiting
- AI model errors
- Database connection issues
- File upload errors (invalid file types, corrupted files, upload failures)
- Gemini Files API errors

All errors return appropriate HTTP status codes and descriptive messages.

**File-specific Error Scenarios:**
- Unsupported file format
- File size limits
- Gemini Files API unavailable
- File processing failures

---
